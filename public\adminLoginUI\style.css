      @font-face {
        font-family: "Source Sans 3";
        src: url("../dist/fonts/SourceSans3-Regular.woff") format("woff2"),
            url("../dist/fonts/SourceSans3-Regular.woff2") format("woff");
        font-weight: 600;
        font-style: normal;
        font-display: swap;
    }   
  :root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
  }
  
  body {
    min-height: 100vh;
    position: relative;
    padding-top: 150px;
    font-family: "Source Sans 3";
    background-image: radial-gradient(circle, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
    background-size: 15px 15px;
    background-attachment: fixed;
  }
  
  .header {
    background: var(--dark-color);
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    width: 100%;
  }
  #current-time,
  #current-date {
    font-size: 1rem;
    font-weight: 500;
  }
  
  #current-time {
    color: #007bff;
  }
  
  #current-date {
    color: #6c757d;
  }
  
  .logo-container {
    display: flex;
    align-items: center;
  }
  
  .logo {
    width: 50px;
    height: 60px;
    display: block;
    overflow: hidden;
    flex-shrink: 0;
  }
  
  .logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    display: block;
  }
  
  .school-info h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 1.2;
    color: #f8f9fa;
    text-shadow: 2px 2px #bba800cc;
  }
  
  .school-info p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.9;
    line-height: 1.2;
  }
  
  .time-info {
    text-align: right;
  }
  
  .time-info p {
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.2;
  }
  
  .login-box {
    margin-bottom: 2.5rem;
  }
  .footer {
    text-align: center;
    padding: 2rem;
    color: var(--secondary-color);
    position: relative;
    z-index: 1;
  }
  
  .footer hr {
    border: none;
    height: 1px;
    background: rgba(0, 0, 0, 0.1);
    margin: 0 0 1rem 0;
  }
  
  .footer p {
    margin: 0.25rem 0;
    font-size: 0.9rem;
  }
  
  @media (max-width: 768px) {
    .header {
      padding: 1rem;
    }
  
    .school-info h1 {
      font-size: 1.1rem;
    }
  
    .school-info p {
      font-size: 0.8rem;
    }
  
    .time-info {
      margin-top: 1rem;
    }
  
    .time-info p {
      font-size: 0.8rem;
    }
    .login-logo {
      margin-top: 30px;
    }
  
    .logo-container {
      display: flex;
      align-items: left;
      justify-content: left;
    }
  }
  
  @media (max-width: 576px) {
    .school-info h1 {
      font-size: 1.2rem;
    }
  
    .school-info p {
      font-size: 0.9rem;
    }
  
    .time-info p {
      font-size: 0.75rem;
    }
    .login-logo {
      margin-top: 25px;
    }
  }
  