<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Category Management</title>
</head>

<body class="layout-fixed layout-navbar-fixed bg-body-secondary">
    <div class="layout-wrapper">
        <!-- Include Header and Navbar -->
        <?php admin_view_layout(['header', 'navbar',]); ?>
        <!-- Main Content -->
        <main class="container container-sm">
            <div class="p-5 bg-white shadow-sm">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-sm-8">
                            <h4 class="mb-0">Candidates</h4>
                        </div>
                        <div class="col-sm-4">
                            <ol class="breadcrumb float-sm-end">
                                <li class="breadcrumb-item"><a href="#">Home</a></li>
                                <li class="breadcrumb-item active" aria-current="page">Candidates</li>
                            </ol>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title mt-2">Candidates List</h3>
                                    <div class="card-tools">
                                        <!-- Trigger Add Category Modal -->
                                        <a href="#addCandidatesModal" data-bs-toggle="modal"
                                            class="btn btn-primary btn-sm">
                                            <i class="bi bi-plus"></i> Add New
                                        </a>
                                    </div>
                                </div>
                                <div class="table-responsive">
                                    <div class="card-body">
                                        <!-- Table with DataTables -->
                                        <table id="categoryTable" class="table table-striped nowrap" style="width:100%">
                                            <thead>
                                                <tr>
                                                    <th>Photo</th>
                                                    <th>Firstname</th>
                                                    <th>Lastname</th>
                                                    <th>Platform</th>
                                                    <th>Date Created</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td><a href="#updateCandidateImageModal" data-bs-toggle="modal">
                                                            <img src="../dist/assets/img/avatar.png"
                                                                alt="Candidate Image" width="60" height="60"
                                                                class="img-thumbnail">
                                                        </a>
                                                    </td>
                                                    <td>John </td>
                                                    <td>Doe</td>
                                                    <td>Free lugaw every monday and friday</td>
                                                    <td>Aug 11, 2025</td>
                                                    <td>
                                                        <!-- Edit and Delete Buttons -->
                                                        <a href="#editCandidateModal" data-bs-toggle="modal"
                                                            class="btn btn-sm btn-warning">
                                                            <i class="bi bi-pencil me-1"></i> Edit
                                                        </a>
                                                        <a href="#deleteCandidateModal" data-bs-toggle="modal"
                                                            class="btn btn-sm btn-danger">
                                                            <i class="bi bi-trash me-1"></i> Delete
                                                        </a>
                                                    </td>

                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </main>
    </div>

    <?php include __DIR__ . '/addCandidate_modal.php'; ?>
    <?php include __DIR__ . '/editCandidate_modal.php' ?>
    <?php include __DIR__ . '/deleteCandidate_modal.php' ?>
    <?php include __DIR__ . '/updateCandidateImage_modal.php' ?>
    <?php admin_view_layout(['footer', 'script',]); ?>

    <script>
        $(document).ready(function () {
            // Initialize DataTable
            $('#categoryTable').DataTable({
                responsive: true,
                paging: true,
                searching: true,
                lengthChange: true,
                info: true
            });
        });
    </script>
</body>

</html>