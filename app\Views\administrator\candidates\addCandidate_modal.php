    <!-- Add Candidate Modal -->
    <div class="modal fade" id="addCandidatesModal" tabindex="-1" aria-labelledby="addCategoryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addCategoryModalLabel">Add New Candidate</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="#" method="POST" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="firstname" class="form-label">Firstname</label>
                                    <input type="text" class="form-control" id="firstname" name="product_name" required>
                                </div>
                                <div class="mb-3">
                                    <label for="lastname" class="form-label">Lastname</label>
                                    <input type="text" class="form-control" id="lastname" name="lastname" required>
                                </div>
                                <div class="mb-3">
                                    <label for="party" class="form-label">Party</label>
                                    <input type="text" class="form-control" id="party" name="party" required>
                                </div>
                                <div class="mb-3">
                                    <label for="dept_id" class="form-label">Position</label>
                                    <select class="form-select" id="dept_id" name="dept_id" required>
                                        <option value="" disabled selected>Select Position</option>
                                        <option value="1">Governor</option>
                                        <option value="2">Vice Governor</option>
                                        <option value="3">Secretary</option>
                                        <option value="4">Treasurer</option>
                                    </select>
                                </div>

                            </div>

                            <!-- Right Column -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="dept_id" class="form-label">Department</label>
                                    <select class="form-select" id="dept_id" name="dept_id" required>
                                        <option value="" disabled selected>Select Department</option>
                                        <option value="1">COED</option>
                                        <option value="2">CICTE</option>
                                        <option value="3">CBM</option>
                                        <option value="4">CONAHS</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="photo" class="form-label">Candidate Image (upload limit is 2MB)</label>
                                    <input class="form-control" type="file" id="photo" name="photo" required accept="image/*" onchange="previewImage()">
                                    <img id="photoImagePreview" src="#" alt="Image Preview" style="max-width: 100%; display: none;">
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-primary">Save Candidate</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <script>
        // Image preview
        function previewImage() {
            var fileInput = document.getElementById('photo');
            var photoImagePreview = document.getElementById('photoImagePreview');

            if (fileInput.files && fileInput.files[0]) {
                var reader = new FileReader();

                reader.onload = function(e) {
                    photoImagePreview.src = e.target.result;
                    photoImagePreview.style.display = 'block';
                }

                reader.readAsDataURL(fileInput.files[0]);
            }
        }
    </script>
