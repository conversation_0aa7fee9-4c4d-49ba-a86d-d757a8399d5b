<!-- Edit Candidate Modal -->
<div class="modal fade" id="editCandidateModal" tabindex="-1" aria-labelledby="editCandidateLabel" aria-hidden="true">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Edit Candidate</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <form id="editCandidateForm">
                <input type="hidden" name="candidate_id" id="edit_candidate_id">

                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_firstname" class="form-label">Firstname</label>
                        <input type="text" class="form-control" id="edit_firstname" name="firstname" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_lastname" class="form-label">Lastname</label>
                        <input type="text" class="form-control" id="edit_lastname" name="lastname" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_platform" class="form-label">Platform</label>
                        <textarea class="form-control" id="edit_platform" name="platform" rows="3" required></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="edit_department_id" class="form-label">Department</label>
                        <select class="form-select" id="edit_department_id" name="department_id" required>
                            <option value="" disabled selected>Select Department</option>
                            <!-- These options can be populated dynamically too -->
                            <option value="1">COED</option>
                            <option value="2">CICTE</option>
                            <option value="3">CBM</option>
                            <option value="4">CONAHS</option>
                        </select>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-warning">Save Candidate</button>
                </div>
            </form>
        </div>
    </div>
</div>