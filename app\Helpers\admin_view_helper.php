<?php
if (!function_exists('admin_view_layout')) {
    /**
     * Load specific parts of the admin layout.
     *
     * @param array $parts The list of layout files to include (without .php extension)
     * @param string $section The section folder (e.g., 'administrator')
     */

    function admin_view_layout(array $parts = ['header', 'navbar', 'footer', 'script'], string $section = 'administrator')
    {

        $base = __DIR__ . "/../views/{$section}/includes";

        foreach ($parts as $part) {
            $file = "$base/$part.php";
            if (file_exists($file)) {
                include $file;
            }
        }
    }
}
