<?php
if (!function_exists('redirect')) {
    function redirect(string $path): void
    {
        // If it's already a full URL, redirect directly
        if (filter_var($path, FILTER_VALIDATE_URL)) {
            header("Location: {$path}");
            exit;
        }

        // Clean path for internal redirects
        $cleanPath = ltrim($path, '/');

        // Redirect to baseurl + clean path
        header("Location: " . rtrim(baseurl(), '/') . '/' . $cleanPath);
        exit;
    }
}
