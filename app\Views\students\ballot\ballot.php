<body class="layout-fixed layout-navbar-fixed bg-body-secondary">
    <div class="layout-wrapper">
        <?php include __DIR__ . '/../includes/navbar.php'; ?>

        <div class="container-fluid py-4">
            <div class="row justify-content-center">
                <div class="col-lg-10 col-xl-8">
                    <!-- Header Section -->
                    <div class="text-center mb-4">
                        <h1 class="display-6 fw-bold text-primary mb-2">DEPARTMENTAL ELECTION</h1>
                        <p class="text-muted fs-5">Cast your vote for the candidates below</p>
                        <div class="alert alert-info d-inline-block" role="alert">
                            <i class="bi bi-info-circle me-2"></i>
                            Please select one candidate for each position
                        </div>
                    </div>

                    <!-- Ballot Form -->
                    <form id="ballotForm" class="needs-validation" novalidate>
                        <h4 class="text-center p-3">
                            College of Information and Communications, Technology and Engineering
                        </h4>
                        <!-- governor Position -->
                        <div class="card shadow-sm border-0 mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">
                                    Governor
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="candidate-card border rounded p-3 h-100">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="governor" id="governor1" value="1" required>
                                                <label class="form-check-label w-100" for="governor1">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div class="d-flex align-items-center">
                                                            <img src="../dist/assets/img/avatar.png" alt="Candidate 1" class="rounded-circle me-3" width="50" height="50">
                                                            <div>
                                                                <h6 class="mb-1 fw-bold">John Doe</h6>
                                                                <small class="text-muted">Computer Science</small>
                                                            </div>
                                                        </div>
                                                        <!-- Party List -->
                                                        <div class="party-list">
                                                            <small class="text-muted">
                                                                <span class="badge bg-light text-dark">VOS</span>
                                                            </small>
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                    </div>
                                    <div class="col-md-6">
                                        <div class="candidate-card border rounded p-3 h-100">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="governor" id="governor2" value="2" required>
                                                <label class="form-check-label w-100" for="governor2">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div class="d-flex align-items-center">
                                                            <img src="../dist/assets/img/avatar.png" alt="Candidate 2" class="rounded-circle me-3" width="50" height="50">
                                                            <div>
                                                                <h6 class="mb-1 fw-bold">Jane Smith</h6>
                                                                <small class="text-muted">Information Technology</small>
                                                            </div>
                                                        </div>
                                                        <!-- Party List -->
                                                        <div class="party-list">
                                                            <small class="text-muted">
                                                                <span class="badge bg-light text-dark">KUPAL</span>
                                                            </small>
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Vice Governor Position -->
                        <div class="card shadow-sm border-0 mb-4">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">
                                    Vice Governor
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="candidate-card border rounded p-3 h-100">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="vice_governor" id="vice_governor1" value="1" required>
                                                <label class="form-check-label w-100" for="vice_governor1">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div class="d-flex align-items-center">
                                                            <img src="../dist/assets/img/avatar.png" alt="Candidate 1" class="rounded-circle me-3" width="50" height="50">
                                                            <div>
                                                                <h6 class="mb-1 fw-bold">Mike Johnson</h6>
                                                                <small class="text-muted">Computer Science</small>
                                                            </div>
                                                        </div>
                                                        <!-- Party List -->
                                                        <div class="party-list">
                                                            <small class="text-muted">
                                                                <span class="badge bg-light text-dark">KUPAL</span>
                                                            </small>
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="candidate-card border rounded p-3 h-100">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="vice_governor" id="vice_governor2" value="2" required>
                                                <label class="form-check-label w-100" for="vice_governor2">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div class="d-flex align-items-center">
                                                            <img src="../dist/assets/img/avatar.png" alt="Candidate 2" class="rounded-circle me-3" width="50" height="50">
                                                            <div>
                                                                <h6 class="mb-1 fw-bold">Sarah Wilson</h6>
                                                                <small class="text-muted">Information Technology</small>
                                                            </div>
                                                        </div>
                                                        <!-- Party List -->
                                                        <div class="party-list">
                                                            <small class="text-muted">
                                                                <span class="badge bg-light text-dark">VOS</span>
                                                            </small>
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Secretary Position -->
                        <div class="card shadow-sm border-0 mb-4">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="card-title mb-0">
                                    Secretary
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="candidate-card border rounded p-3 h-100">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="secretary" id="secretary1" value="1" required>
                                                <label class="form-check-label w-100" for="secretary1">
                                                    <div class="d-flex align-items-center">
                                                        <img src="../dist/assets/img/avatar.png" alt="Candidate 1" class="rounded-circle me-3" width="50" height="50">
                                                        <div>
                                                            <h6 class="mb-1 fw-bold">Alex Brown</h6>
                                                            <small class="text-muted">Computer Science</small>
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="candidate-card border rounded p-3 h-100">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="secretary" id="secretary2" value="2" required>
                                                <label class="form-check-label w-100" for="secretary2">
                                                    <div class="d-flex align-items-center">
                                                        <img src="../dist/assets/img/avatar.png" alt="Candidate 2" class="rounded-circle me-3" width="50" height="50">
                                                        <div>
                                                            <h6 class="mb-1 fw-bold">Emily Davis</h6>
                                                            <small class="text-muted">Information Technology</small>
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Treasurer Position -->
                        <div class="card shadow-sm border-0 mb-4">
                            <div class="card-header bg-info text-white">
                                <h5 class="card-title mb-0">
                                    Treasurer
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="candidate-card border rounded p-3 h-100">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="treasurer" id="treasurer1" value="1" required>
                                                <label class="form-check-label w-100" for="treasurer1">
                                                    <div class="d-flex align-items-center">
                                                        <img src="../dist/assets/img/avatar.png" alt="Candidate 1" class="rounded-circle me-3" width="50" height="50">
                                                        <div>
                                                            <h6 class="mb-1 fw-bold">David Lee</h6>
                                                            <small class="text-muted">Computer Science</small>
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="candidate-card border rounded p-3 h-100">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="treasurer" id="treasurer2" value="2" required>
                                                <label class="form-check-label w-100" for="treasurer2">
                                                    <div class="d-flex align-items-center">
                                                        <img src="../dist/assets/img/avatar.png" alt="Candidate 2" class="rounded-circle me-3" width="50" height="50">
                                                        <div>
                                                            <h6 class="mb-1 fw-bold">Lisa Garcia</h6>
                                                            <small class="text-muted">Information Technology</small>
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 1st Year Representative Position -->
                        <div class="card shadow-sm border-0 mb-4">
                            <div class="card-header bg-secondary text-white">
                                <h5 class="card-title mb-0">
                                    1st Year Representative
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="candidate-card border rounded p-3 h-100">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="first_yr_rep" id="first_yr_rep1" value="1" required>
                                                <label class="form-check-label w-100" for="first_yr_rep1">
                                                    <div class="d-flex align-items-center">
                                                        <img src="../dist/assets/img/avatar.png" alt="Candidate 1" class="rounded-circle me-3" width="50" height="50">
                                                        <div>
                                                            <h6 class="mb-1 fw-bold">David Lee</h6>
                                                            <small class="text-muted">Computer Science</small>
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="candidate-card border rounded p-3 h-100">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="first_yr_rep" id="first_yr_rep2" value="2" required>
                                                <label class="form-check-label w-100" for="first_yr_rep2">
                                                    <div class="d-flex align-items-center">
                                                        <img src="../dist/assets/img/avatar.png" alt="Candidate 2" class="rounded-circle me-3" width="50" height="50">
                                                        <div>
                                                            <h6 class="mb-1 fw-bold">Lisa Garcia</h6>
                                                            <small class="text-muted">Information Technology</small>
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 2nd Year Representative Position -->
                        <div class="card shadow-sm border-0 mb-4">
                            <div class="card-header bg-secondary text-white">
                                <h5 class="card-title mb-0">
                                    2nd Year Representative
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="candidate-card border rounded p-3 h-100">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="second_yr_rep" id="second_yr_rep1" value="1" required>
                                                <label class="form-check-label w-100" for="second_yr_rep1">
                                                    <div class="d-flex align-items-center">
                                                        <img src="../dist/assets/img/avatar.png" alt="Candidate 1" class="rounded-circle me-3" width="50" height="50">
                                                        <div>
                                                            <h6 class="mb-1 fw-bold">David Lee</h6>
                                                            <small class="text-muted">Computer Science</small>
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="candidate-card border rounded p-3 h-100">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="second_yr_rep" id="second_yr_rep2" value="2" required>
                                                <label class="form-check-label w-100" for="second_yr_rep2">
                                                    <div class="d-flex align-items-center">
                                                        <img src="../dist/assets/img/avatar.png" alt="Candidate 2" class="rounded-circle me-3" width="50" height="50">
                                                        <div>
                                                            <h6 class="mb-1 fw-bold">Lisa Garcia</h6>
                                                            <small class="text-muted">Information Technology</small>
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 3rd Year Representative Position -->
                        <div class="card shadow-sm border-0 mb-4">
                            <div class="card-header bg-secondary text-white">
                                <h5 class="card-title mb-0">
                                    3rd Year Representative
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="candidate-card border rounded p-3 h-100">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="third_yr_rep" id="third_yr_rep1" value="1" required>
                                                <label class="form-check-label w-100" for="third_yr_rep1">
                                                    <div class="d-flex align-items-center">
                                                        <img src="../dist/assets/img/avatar.png" alt="Candidate 1" class="rounded-circle me-3" width="50" height="50">
                                                        <div>
                                                            <h6 class="mb-1 fw-bold">David Lee</h6>
                                                            <small class="text-muted">Computer Science</small>
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="candidate-card border rounded p-3 h-100">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="third_yr_rep" id="third_yr_rep2" value="2" required>
                                                <label class="form-check-label w-100" for="third_yr_rep2">
                                                    <div class="d-flex align-items-center">
                                                        <img src="../dist/assets/img/avatar.png" alt="Candidate 2" class="rounded-circle me-3" width="50" height="50">
                                                        <div>
                                                            <h6 class="mb-1 fw-bold">Lisa Garcia</h6>
                                                            <small class="text-muted">Information Technology</small>
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 4th Year Representative Position -->
                        <div class="card shadow-sm border-0 mb-4">
                            <div class="card-header bg-secondary text-white">
                                <h5 class="card-title mb-0">
                                    4th Year Representative
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="candidate-card border rounded p-3 h-100">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="fourth_yr_rep" id="fourth_yr_rep1" value="1" required>
                                                <label class="form-check-label w-100" for="fourth_yr_rep1">
                                                    <div class="d-flex align-items-center">
                                                        <img src="../dist/assets/img/avatar.png" alt="Candidate 1" class="rounded-circle me-3" width="50" height="50">
                                                        <div>
                                                            <h6 class="mb-1 fw-bold">David Lee</h6>
                                                            <small class="text-muted">Computer Science</small>
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="candidate-card border rounded p-3 h-100">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="fourth_yr_rep" id="fourth_yr_rep2" value="2" required>
                                                <label class="form-check-label w-100" for="fourth_yr_rep2">
                                                    <div class="d-flex align-items-center">
                                                        <img src="../dist/assets/img/avatar.png" alt="Candidate 2" class="rounded-circle me-3" width="50" height="50">
                                                        <div>
                                                            <h6 class="mb-1 fw-bold">Lisa Garcia</h6>
                                                            <small class="text-muted">Information Technology</small>
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Section -->
                        <div class="card shadow-sm border-0 mb-4">
                            <div class="card-body text-center">
                                <div class="alert alert-warning" role="alert">
                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                    <strong>Important:</strong> Once you submit your ballot, you cannot change your vote. Please review your selections carefully.
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                    <button type="button" class="btn btn-outline-secondary" onclick="previewBallot()">
                                        <i class="bi bi-eye me-2"></i>Preview Ballot
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-2"></i>Submit Ballot
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Preview Modal -->
    <div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="previewModalLabel">
                        Ballot Preview
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="previewContent">
                    <!-- Preview content will be populated here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="submitBallot()">Confirm & Submit</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Preview ballot function
        function previewBallot() {
            const form = document.getElementById('ballotForm');
            const formData = new FormData(form);
            let previewHTML = '<div class="text-center mb-3"><h6 class="text-muted">Your Selections:</h6></div>';

            const positions = {
                'governor': 'Governor',
                'vice_governor': 'Vice Governor',
                'secretary': 'Secretary',
                'treasurer': 'Treasurer',
                'first_yr_rep': 'First Year Representative',
                'second_yr_rep': 'Second Year Representative',
                'third_yr_rep': 'Third Year Representative',
                'fourth_yr_rep': 'Fourth Year Representative'
            };

            let hasSelections = false;

            for (const [position, label] of Object.entries(positions)) {
                const selected = formData.get(position);
                if (selected) {
                    hasSelections = true;
                    const candidateName = document.querySelector(`input[name="${position}"]:checked + label h6`).textContent;
                    previewHTML += `
                        <div class="alert alert-success">
                            <strong>${label}:</strong> ${candidateName}
                        </div>
                    `;
                }
            }

            if (!hasSelections) {
                previewHTML = '<div class="alert alert-warning">Please select candidates for all positions before previewing.</div>';
            }

            document.getElementById('previewContent').innerHTML = previewHTML;

            const modal = new bootstrap.Modal(document.getElementById('previewModal'));
            modal.show();
        }

        // Submit ballot function
        function submitBallot() {
            const form = document.getElementById('ballotForm');
            if (form.checkValidity()) {
                // Here you would typically send the data to your backend
                alert('Ballot submitted successfully!');
                // Close modal
                bootstrap.Modal.getInstance(document.getElementById('previewModal')).hide();
            } else {
                form.reportValidity();
            }
        }

        // Form validation
        document.getElementById('ballotForm').addEventListener('submit', function(e) {
            e.preventDefault();
            if (this.checkValidity()) {
                submitBallot();
            } else {
                this.reportValidity();
            }
        });

        // Add hover effects to candidate cards
        document.querySelectorAll('.candidate-card').forEach(card => {
            card.addEventListener('click', function() {
                const radio = this.querySelector('input[type="radio"]');
                if (radio) {
                    radio.checked = true;
                }
            });
        });
    </script>

    <?php include __DIR__ . '/../includes/footer.php'; ?>
    <?php include __DIR__ . '/../includes/script.php'; ?>
</body>
