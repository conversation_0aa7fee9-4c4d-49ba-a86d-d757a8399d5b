<?php
namespace App\Core;

class Router
{
    protected $routes = [];

    public function get($uri, $action)
    {
        $this->routes['GET'][$this->normalize($uri)] = $action;
    }
    public function post($uri, $action)
    {
        $this->routes['POST'][$this->normalize($uri)] = $action;
    }



    protected function normalize($uri)
    {
        return '/' . trim($uri, '/');
    }

    public function dispatch($uri)
    {
        $uri = $this->normalize($uri);
        $method = strtoupper($_SERVER['REQUEST_METHOD']);

        if (isset($this->routes[$method][$uri])) {
            $this->callAction($this->routes[$method][$uri]);
        } else {
            $this->errorPage(404);
        }
    }


    protected function callAction($action)
    {
        [$controller, $method] = explode('@', $action);
        $controller = "App\\Controllers\\" . str_replace('/', '\\', $controller);

        if (class_exists($controller)) {
            $obj = new $controller();
            if (method_exists($obj, $method)) {
                $obj->$method();
                return;
            }
        }

        $this->errorPage(500);
    }

    protected function errorPage(int $code)
    {
        http_response_code($code);

        $errorView = __DIR__ . "/../Views/Errors/{$code}.php";
        if (file_exists($errorView)) {
            require $errorView;
        } else {
            echo "{$code} Error";
        }
        exit;
    }
}
