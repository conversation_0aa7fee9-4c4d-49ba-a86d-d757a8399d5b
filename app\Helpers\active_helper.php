<?php

if (!function_exists('is_active')) {
    function is_active(string $path, bool $section = false): string
    {
        $base = rtrim(parse_url(URLROOT, PHP_URL_PATH) ?: '', '/');
        $curr = preg_replace("#^{$base}#", '', parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH) ?? '/');
        $curr = '/' . trim($curr, '/');
        $path = '/' . trim($path, '/');

        return $section
            ? (str_starts_with($curr, $path) ? 'active' : '')
            : ($curr === $path ? 'active' : '');
    }
}
