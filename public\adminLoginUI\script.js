     // Update time and date
     function updateDateTime() {
        const now = new Date();
        
        // Format time
        const timeString = now.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: true
        });
        
        // Format date
        const dateString = now.toLocaleDateString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
        
        document.getElementById('current-time').textContent = `${timeString}`;
        document.getElementById('current-date').textContent = `${dateString}`;
      }

      // Update time every second
      updateDateTime();
      setInterval(updateDateTime, 1000);

          // Typewriter effect for login message
    function typeWriter() {
        const text = "State University of Northern Negros";
        const element = document.getElementById('typewriter-text');
        let i = 0;

        function type() {
            if (i < text.length) {
                element.innerHTML = text.substring(0, i + 1);
                element.classList.add('typewriter');
                i++;
                setTimeout(type, 50);
            } else {
                // After typing is complete, wait then restart
                setTimeout(() => {
                    element.classList.remove('typewriter');
                    setTimeout(() => {
                        i = 0;
                        element.innerHTML = '';
                        type();
                    }, 250);
                }, 2000);
            }
        }

        type();
    }
    // Start typewriter effect when page loads
    document.addEventListener('DOMContentLoaded', typeWriter);