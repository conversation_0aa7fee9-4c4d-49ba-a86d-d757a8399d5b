<!doctype html>
<html lang="en">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>SUNN Departmental Voting System Ballot</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="title" content="SUNN Departmental Voting System Ballot" />
    <meta name="author" content="SUNN Voting System" />
    <meta name="description" content="SUNN Voting System Voting System Admin Panel" />
    <!-- <link rel="stylesheet" href="<?= baseurl('libs/font/bootstrap-icons.min.css') ?>" />
    <link rel="stylesheet" href="<?= baseurl('dist/css/adminlte.min.css') ?>" />
    <link rel="stylesheet" href="<?= baseurl('public/adminLoginUI/style.css') ?>"> -->
    <link rel="stylesheet" href="../dist/css/adminlte.min.css">
    <link rel="stylesheet" href="../libs/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="../libs/bootstrap/css/bootstrap.min.css">
</head>
<style>
    @font-face {
        font-family: "Source Sans 3";
        src: url("../dist/fonts/SourceSans3-Regular.woff") format("woff2"),
            url("../dist/fonts/SourceSans3-Regular.woff2") format("woff");
        font-weight: 600;
        font-style: normal;
        font-display: swap;
    }

    body {
        min-height: 100vh;
        position: relative;
        font-family: "Source Sans 3";
        background-image: radial-gradient(circle, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
        background-size: 15px 15px;
        background-attachment: fixed;
    }

    .candidate-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .candidate-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border-color: var(--bs-primary) !important;
    }

    .form-check-input:checked+.form-check-label .candidate-card {
        border-color: var(--bs-primary) !important;
        background-color: var(--bs-primary-bg-subtle);
    }

    .card-header {
        border-bottom: none;
    }

    .form-check-label {
        cursor: pointer;
    }

    .form-check-input:checked {
        background-color: var(--bs-primary);
        border-color: var(--bs-primary);
    }
</style>
<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
    <div class="container">
        <a class="navbar-brand d-flex align-items-center" href="#">
            <img src="../dist/assets/img/avatar.png" alt="Logo" width="30" height="30" class="rounded-circle me-2">
            <span><?= isset($voter_name) ? $voter_name : 'Voter\'s Name' ?></span>
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                <!-- You can add additional nav items here if needed -->
            </ul>
            <ul class="d-flex navbar-nav justify-content-between w-100">
                <li class="nav-item ms-auto">
                    <a class="btn btn-danger d-sm-inline-block" href="<?= baseurl('logout') ?>">
                        <i class="bi bi-box-arrow-right"></i> Logout
                    </a>
                </li>
            </ul>

        </div>
    </div>
</nav>
