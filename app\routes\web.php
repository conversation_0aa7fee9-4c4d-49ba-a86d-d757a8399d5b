<?php

// students routing 
$router->get('/', 'Students/HomeController@index');
$router->get('/studentreg', 'Students/StudentRegController@index');
$router->get('/students/ballot', 'Students/Ballot/BallotController@index');
$router->get('/students/example', 'Students/Example/ExampleController@index');
// $router->get('/student/studentreg', 'Student/StudentReg/StudentRegController@index');


// administrator routing
$router->get('/administrator/dashboard', 'Administrator/Dashboard/DashboardController@index');
$router->get('/administrator/candidates', 'Administrator/Candidates/CandidatesController@index');
$router->get('/administrator/voters', 'Administrator/Voters/VotersController@index');
$router->get('/administrator/reports', 'Administrator/Reports/ReportsController@index');
$router->get('/administrator/tally', 'Administrator/Tally/TallyController@index');
$router->get('/administrator/department', 'Administrator/Department/DepartmentController@index');
$router->get('/administrator/positions', 'Administrator/Positions/PositionsController@index');



// administrator login routing
$router->get('/administrator/login', 'Administrator/Login/LoginController@index');
$router->post('/administrator/login/authenticate', 'Administrator/Login/LoginController@authenticate');
$router->get('administrator/logout', 'Administrator\Login\LoginController@logout');

// Comselect routing
$router->get('/comselec/login', 'ComSelec/Login/LoginController@index');
