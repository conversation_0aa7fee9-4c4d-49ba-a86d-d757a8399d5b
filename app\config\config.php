<?php
/**
 * Load .env and define global constants
 */
function loadConfig()
{
    $envFile = __DIR__ . '/../../.env';

    if (!file_exists($envFile)) {
        throw new Exception(".env file not found at {$envFile}");
    }

    // Read .env line by line
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue; // Skip comments
        }
        [$name, $value] = array_map('trim', explode('=', $line, 2));
        $value = trim($value, "\"'");

        $_ENV[$name] = $value;
        putenv("$name=$value");
    }

    // Define constants based on .env
    define('APPROOT', dirname(dirname(__FILE__)));
    define('URLROOT', rtrim($_ENV['APP_URL'], '/'));
    define('SITENAME', $_ENV['APP_NAME']);
}
