<?php

namespace App\Controllers\Administrator\Login;

use function set_flash;
use App\Core\Controller;
use App\Models\Administrator\Login\Login as LoginModel;

class LoginController extends Controller
{
    public function index()
    {
        $this->view('administrator/login/login');
    }

    public function authenticate()
    {
        // Ensure POST request
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('/administrator/login');
            return;
        }

        // Get form data safely
        $username = trim((string) filter_input(INPUT_POST, 'username', FILTER_SANITIZE_FULL_SPECIAL_CHARS));
        $password = $_POST['password'] ?? '';

        // If either field is empty, flash and redirect
        if ($username === '' || $password === '') {
            set_flash('error', 'Please enter both username and password.');
            redirect('/administrator/login');
            return;
        }

        // Fetch admin user
        $adminModel = new LoginModel();
        $admin = $adminModel->getByUsername($username);

        // Verify credentials
        if (!$admin || !isset($admin['password']) || !password_verify($password, $admin['password'])) {
            set_flash('error', 'Invalid username or password');
            redirect('/administrator/login');
            return;
        }

        // Set session and redirect
        session_regenerate_id(true);
        $_SESSION['admin'] = [
            'id' => $admin['id'] ?? null,
            'username' => $admin['username'] ?? '',
            'role' => $admin['user_type'] ?? '',
        ];

        redirect('/administrator/dashboard');
    }

    public function logout()
    {
        unset($_SESSION['admin']);
        session_regenerate_id(true);
        redirect('/administrator/login');
        exit;
    }
}
