<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>

<body class="login-page bg-body-secondary">
    <?php students_view_layout(['header']); ?>

    <div class="login-box mt-3">
        <!-- <div class="login-logo fw-bold text-muted">
            <b>SUNN e-Election</b>
        </div> -->
        <div class="card">
            <div class="card-body login-card-body">
                <h4 class="login-box-msg text-info">Student Register</h4>
                <form action="" method="post" novalidate="novalidate" enctype="multipart/form-data">
                    <div class="form-group mb-3">
                        <label for="id-number" class="form-label w-100"> <label class="control-label">ID Number <span
                                    class="text-danger">*</span>(Example: 21-1234) No extra spaces</label></label>
                        <input type="text" id="id-number" name="id_number" class="form-control bg-body-secondary"
                            placeholder="ID Number" required aria-required="true" />
                    </div>

                    <div class="form-group mb-3">
                        <label for="lastname" class="form-label w-100">Lastname <span class="text-danger">*</span>(use n
                            for ñ)</label>
                        <input type="text" id="lastname" name="lastname" class="form-control bg-body-secondary"
                            placeholder="Lastname" required aria-required="true" />
                    </div>

                    <div class="form-group mb-3">
                        <label for="cell-num" class="form-label w-100">Cellphone Number (Optional)</label>
                        <input type="text" id="cell-num" name="cell-num" class="form-control bg-body-secondary"
                            placeholder="Cellphone Number" required aria-required="true" />
                    </div>

                    <div class="form-group mb-3">
                        <label for="email" class="form-label w-100">E-mail <span class="text-danger">*</span></label>
                        <input type="email" id="email" name="email" class="form-control bg-body-secondary"
                            placeholder="E-mail" required aria-required="true" />
                    </div>

                    <div class="form-group mb-3">
                        <label class="control-label">Course <span class="text-danger">*</span></label>
                        <select class="form-control" name="course" required aria-required="true">
                            <option value="">Select Course</option>
                            <option value="SUNN">SUNN</option>
                        </select>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-info text-light">REGISTER</button>
                            </div>
                        </div>
                        <hr class="divider mt-3">
                    </div>
                    <div class="login-options" style="text-align: center;">
                        <span class="text-danger">*</span> - Required <br>
                        <p>Have an account? Click here to <a href="./">Login</a></p>
                    </div>
                </form>
            </div>
        </div>
        <!-- <footer class="footer">
        <hr class="divider">
        <p>Copyright © 2024 </p>
        <p>Programmed by:  :-P</p>
    </footer> -->

        <?php students_view_layout(['script']); ?>
</body>

</html>